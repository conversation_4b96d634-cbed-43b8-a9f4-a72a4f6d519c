{"data_mtime": 1753060937, "dep_lines": [7, 8, 9, 10, 11, 12, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["json", "re", "os", "typing", "dataclasses", "logging", "json5", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "json.decoder", "types"], "hash": "00d470513e195010866b2efa88fb3576c84e9db6", "id": "intent_detection_service", "ignore_all": false, "interface_hash": "5031a1f8070b1fe7f07f837c01cb81d1b14b5bdd", "mtime": 1753060936, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\intent_detection_service.py", "plugin_data": null, "size": 14745, "suppressed": [], "version_id": "1.15.0"}