"""
Test search and query functionality.
"""
import pytest
import httpx


class TestSearchFunctionality:
    """Test search and query endpoints."""

    @pytest.mark.asyncio
    async def test_enhanced_search(self, http_client: httpx.AsyncClient, sample_codebase_name, sample_query):
        """Test the enhanced search endpoint."""
        payload = {
            "codebase_name": sample_codebase_name,
            "query": sample_query,
            "max_results": 5
        }
        response = await http_client.post("/tools/enhanced_search", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "result" in data

    @pytest.mark.asyncio
    async def test_get_optimized_context(self, http_client: httpx.AsyncClient, sample_codebase_name, sample_query):
        """Test the optimized context endpoint."""
        payload = {
            "codebase_name": sample_codebase_name,
            "query": sample_query
        }
        response = await http_client.post("/tools/get_optimized_context", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "context" in data
        assert "chunk_count" in data
        assert "query" in data

    @pytest.mark.asyncio
    async def test_legacy_search_code(self, http_client: httpx.AsyncClient, sample_codebase_name, sample_query):
        """Test the legacy search code endpoint."""
        payload = {
            "codebase_name": sample_codebase_name,
            "query": sample_query
        }
        response = await http_client.post("/tools/search_code", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "result" in data

    @pytest.mark.asyncio
    async def test_legacy_ask_about_code(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test the legacy ask about code endpoint."""
        payload = {
            "codebase_name": sample_codebase_name,
            "question": "What are the main functions in this codebase?"
        }
        response = await http_client.post("/tools/ask_about_code", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "result" in data

    @pytest.mark.asyncio
    async def test_search_endpoint_stateless(self, http_client: httpx.AsyncClient, sample_codebase_name, sample_query):
        """Test the stateless search endpoint."""
        payload = {
            "codebase_name": sample_codebase_name,
            "query": sample_query,
            "max_results": 3
        }
        response = await http_client.post("/search", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "results" in data or "error" in data

    @pytest.mark.asyncio
    async def test_context_endpoint(self, http_client: httpx.AsyncClient, sample_query):
        """Test the context endpoint (requires codebase selection)."""
        payload = {"query": sample_query}
        response = await http_client.post("/context", json=payload)
        
        # May return 400 if no codebase selected, which is expected
        assert response.status_code in [200, 400]
