{"unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_enhanced_context_endpoint": true, "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_list_codebases": true, "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_list_codebases_get_method": true, "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_legacy_codebases_endpoint": true, "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_select_codebase": true, "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_get_enhanced_stats": true, "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_session_codebase_management": true, "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_framework_status": true, "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_status": true, "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_refresh": true, "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_recommendations": true, "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_framework_query": true, "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_analyze_codebase_endpoint": true, "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_get_patterns_endpoint": true, "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_enhance_query_endpoint": true, "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_enhanced_search": true, "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_get_optimized_context": true, "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_legacy_search_code": true, "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_legacy_ask_about_code": true, "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_search_endpoint_stateless": true, "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_context_endpoint": true, "unit-tests/test_server_health.py::TestServerHealth::test_root_endpoint": true, "unit-tests/test_server_health.py::TestServerHealth::test_health_endpoint": true, "unit-tests/test_server_health.py::TestServerHealth::test_status_endpoint": true, "unit-tests/test_server_health.py::TestServerHealth::test_detailed_health_endpoint": true, "unit-tests/test_server_health.py::TestServerHealth::test_enhanced_features_endpoint": true}