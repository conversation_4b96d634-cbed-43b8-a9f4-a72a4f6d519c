# VS Code Testing Troubleshooting Guide

## Issue: "Invalid test discovery output" Error

The error you're seeing is caused by a conflict between the third-party Python Test Adapter extension and the built-in VS Code Python testing support.

## Solution Steps

### 1. Disable Third-Party Test Extensions

**Disable these extensions if installed:**
- Python Test Explorer for Visual Studio Code (littlefoxteam.vscode-python-test-adapter)
- Test Explorer UI (hbenl.vscode-test-explorer)

**How to disable:**
1. Open VS Code Extensions panel (Ctrl+Shift+X)
2. Search for "Python Test Explorer" or "Test Explorer"
3. Click the gear icon and select "Disable"
4. Restart VS Code

### 2. Use Built-in Python Testing

The project is now configured to use VS Code's built-in Python testing support:

**Settings configured in `.vscode/settings.json`:**
```json
{
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.pytestArgs": ["unit-tests", "-v", "--tb=short"],
    "testExplorer.useNativeTesting": true
}
```

### 3. Verify Test Discovery

**Method 1: Command Palette**
1. Open Command Palette (Ctrl+Shift+P)
2. Type "Python: Refresh Tests"
3. Select and run the command

**Method 2: Test Panel**
1. Open the Test panel (Ctrl+Shift+T or click the test beaker icon)
2. Tests should appear automatically
3. If not, click the refresh button in the test panel

### 4. Manual Test Verification

**Run tests from terminal to verify they work:**
```bash
# Test the framework is working
python -m pytest unit-tests/test_simple_verification.py -v

# Test server connectivity (requires server running)
python -m pytest unit-tests/test_basic_connectivity.py -v

# Run all tests
python unit-tests/run_tests.py --verbose
```

### 5. Alternative: Use Debug Configurations

If test discovery still doesn't work, use the debug configurations:

1. Open Run and Debug panel (Ctrl+Shift+D)
2. Select "Run Unit Tests" from the dropdown
3. Click the play button to run tests

## Expected Behavior

### When Tests Work Correctly:
- Tests appear in the VS Code Test Explorer
- You can run individual tests or test suites
- Test results show pass/fail status
- You can debug tests by setting breakpoints

### Test Files Available:
- `test_simple_verification.py` - Basic tests (no server required)
- `test_basic_connectivity.py` - Server connectivity tests
- `test_server_health.py` - Health endpoint tests
- `test_codebase_management.py` - Codebase management tests
- `test_search_functionality.py` - Search functionality tests
- `test_framework_integration.py` - Framework integration tests
- `test_intent_detection.py` - Intent detection tests
- `test_analysis_endpoints.py` - Analysis endpoint tests

## Quick Test Commands

### Run Simple Tests (No Server Required)
```bash
python -m pytest unit-tests/test_simple_verification.py -v
```

### Run Server Tests (Server Required on Port 5002)
```bash
python -m pytest unit-tests/test_basic_connectivity.py -v
```

### Run All Tests
```bash
python unit-tests/run_tests.py --verbose
```

## Common Issues and Solutions

### Issue: "No tests discovered"
**Solution:** 
- Ensure Python interpreter is correctly set
- Check that pytest is installed: `pip install pytest pytest-asyncio httpx`
- Reload VS Code window (Ctrl+Shift+P → "Developer: Reload Window")

### Issue: "Module not found" errors
**Solution:**
- Ensure you're in the correct workspace directory
- Check that PYTHONPATH includes the workspace folder
- Verify requirements.txt dependencies are installed

### Issue: Tests fail with connection errors
**Solution:**
- Start the code analysis server on port 5002
- Run simple verification tests first: `test_simple_verification.py`
- Check server health at `http://localhost:5002/health`

## Verification Steps

1. **Test Framework Works:**
   ```bash
   python -m pytest unit-tests/test_simple_verification.py -v
   ```
   Should show 11 passing tests.

2. **VS Code Integration:**
   - Open Test Explorer panel
   - Should see test files and individual tests
   - Can run tests by clicking play buttons

3. **Server Integration:**
   - Start your code analysis server on port 5002
   - Run: `python -m pytest unit-tests/test_basic_connectivity.py -v`
   - Should connect successfully

## Support

If issues persist:
1. Check VS Code Python extension is up to date
2. Verify Python interpreter path in VS Code
3. Try running tests from command line first
4. Check VS Code Developer Console for additional error details

The testing framework is working correctly - the issue is with VS Code test discovery configuration, not the tests themselves.
