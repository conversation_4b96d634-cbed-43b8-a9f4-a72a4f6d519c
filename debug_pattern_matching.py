#!/usr/bin/env python3
"""
Debug why specific management patterns aren't matching
"""
import asyncio
import aiohttp
import json

async def debug_pattern_matching():
    test_cases = [
        {
            "query": "switch to bookstore codebase",
            "expected_pattern": "switch to codebase",
            "expected_intent": "management"
        },
        {
            "query": "create new codebase", 
            "expected_pattern": "create codebase",
            "expected_intent": "management"
        }
    ]
    
    print("🔍 Debugging Pattern Matching Issues")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        for case in test_cases:
            query = case["query"]
            expected_pattern = case["expected_pattern"]
            expected_intent = case["expected_intent"]
            
            print(f"\n🧪 Testing: '{query}'")
            print(f"   Expected Pattern: '{expected_pattern}'")
            print(f"   Expected Intent: {expected_intent}")
            print("-" * 40)
            
            try:
                async with session.post(
                    'http://home-ai-server.local:5002/tools/detect_intent',
                    json={'query': query}
                ) as response:
                    result = await response.json()
                    
                    intent = result.get('intent', 'unknown')
                    patterns = result.get('matched_patterns', [])
                    
                    # Check if expected pattern was found
                    mgmt_patterns = [p for p in patterns if "(management)" in p]
                    pattern_found = any(expected_pattern in p for p in mgmt_patterns)
                    
                    print(f"   Actual Intent: {intent}")
                    print(f"   All Patterns: {patterns}")
                    print(f"   Management Patterns: {mgmt_patterns}")
                    print(f"   Expected Pattern Found: {pattern_found}")
                    
                    # Manual pattern check
                    query_lower = query.lower()
                    expected_lower = expected_pattern.lower()
                    manual_match = expected_lower in query_lower
                    print(f"   Manual Pattern Check: '{expected_lower}' in '{query_lower}' = {manual_match}")
                    
                    if not pattern_found and manual_match:
                        print("   ❌ PATTERN SHOULD MATCH BUT DOESN'T!")
                    elif pattern_found and intent != expected_intent:
                        print("   ❌ PATTERN MATCHES BUT WRONG INTENT!")
                    elif pattern_found and intent == expected_intent:
                        print("   ✅ WORKING CORRECTLY")
                    else:
                        print("   ⚠️ UNCLEAR ISSUE")
                    
            except Exception as e:
                print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_pattern_matching())
