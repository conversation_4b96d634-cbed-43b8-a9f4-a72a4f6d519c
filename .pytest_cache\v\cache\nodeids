["test_openwebui_api.py::test_with_codebase_selection", "test_openwebui_api.py::test_without_codebase_selection", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_analysis_health", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_analysis_status", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_analyze_codebase_tool_endpoint", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_enhanced_ask_endpoint", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_enhanced_context_endpoint", "unit-tests/test_basic_connectivity.py::TestBasicConnectivity::test_health_endpoint_basic", "unit-tests/test_basic_connectivity.py::TestBasicConnectivity::test_list_codebases_basic", "unit-tests/test_basic_connectivity.py::TestBasicConnectivity::test_server_is_running", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_get_enhanced_stats", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_legacy_codebases_endpoint", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_list_codebases", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_list_codebases_get_method", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_select_codebase", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_session_codebase_management", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_analyze_codebase_endpoint", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_enhance_query_endpoint", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_framework_query", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_framework_status", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_get_patterns_endpoint", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_recommendations", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_refresh", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_status", "unit-tests/test_intent_detection.py::TestIntentDetection::test_detect_intent", "unit-tests/test_intent_detection.py::TestIntentDetection::test_intent_config", "unit-tests/test_intent_detection.py::TestIntentDetection::test_intent_detection_empty_query", "unit-tests/test_intent_detection.py::TestIntentDetection::test_intent_detection_with_various_queries", "unit-tests/test_intent_detection.py::TestIntentDetection::test_reload_intent_config", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_context_endpoint", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_enhanced_search", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_get_optimized_context", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_legacy_ask_about_code", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_legacy_search_code", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_search_endpoint_stateless", "unit-tests/test_server_health.py::TestServerHealth::test_detailed_health_endpoint", "unit-tests/test_server_health.py::TestServerHealth::test_enhanced_features_endpoint", "unit-tests/test_server_health.py::TestServerHealth::test_health_endpoint", "unit-tests/test_server_health.py::TestServerHealth::test_root_endpoint", "unit-tests/test_server_health.py::TestServerHealth::test_status_endpoint", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_async_functionality", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_basic_math", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_configuration_values", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_exception_handling", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_list_operations", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[1-2]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[2-4]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[3-6]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[4-8]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_pytest_markers", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_string_operations"]