["test_openwebui_api.py::test_with_codebase_selection", "test_openwebui_api.py::test_without_codebase_selection", "unit-tests/test_basic_connectivity.py::TestBasicConnectivity::test_server_is_running", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_async_functionality", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_basic_math", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_configuration_values", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_exception_handling", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_list_operations", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[1-2]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[2-4]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[3-6]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[4-8]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_pytest_markers", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_string_operations"]