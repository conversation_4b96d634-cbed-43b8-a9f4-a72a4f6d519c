#!/usr/bin/env python3
"""
Debug the specific "delete codebase" query to understand why it's not routing to management
"""
import asyncio
import aiohttp
import json

async def debug_delete_query():
    queries = [
        "delete codebase test_project",
        "switch to bookstore codebase",
        "remove codebase utils",
        "create new codebase",
        "update codebase index"
    ]
    
    print("🔍 Testing Management Query Fixes")
    print("=" * 60)

    async with aiohttp.ClientSession() as session:
        for query in queries:
            print(f"\n🧪 Testing: '{query}'")
            print("-" * 40)

            try:
                async with session.post(
                    'http://home-ai-server.local:5002/tools/detect_intent',
                    json={'query': query}
                ) as response:
                    result = await response.json()

                    intent = result.get('intent', 'unknown')
                    confidence = result.get('confidence', 0)
                    action = result.get('suggested_action', 'unknown')

                    # Status indicator
                    status = "✅" if intent == "management" else "❌" if intent == "code_analysis" else "⚠️"

                    print(f"{status} Intent: {intent} (confidence: {confidence:.2f})")
                    print(f"   Action: {action}")

                    # Show confidence breakdown
                    debug_info = result.get('debug_info', {})
                    confidence_scores = debug_info.get('confidence_scores', {})
                    if confidence_scores:
                        print("   Confidence Scores:")
                        for intent_name, score in confidence_scores.items():
                            print(f"     • {intent_name}: {score:.2f}")

                    # Show key patterns
                    patterns = result.get('matched_patterns', [])
                    mgmt_patterns = [p for p in patterns if "(management)" in p]
                    if mgmt_patterns:
                        print(f"   Management Patterns: {mgmt_patterns}")

            except Exception as e:
                print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_delete_query())
