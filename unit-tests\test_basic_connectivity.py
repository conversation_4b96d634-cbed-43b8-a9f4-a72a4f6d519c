"""
Basic connectivity test to verify the server is accessible.
This is a simple test that can be run first to ensure the server is up.
"""
import pytest
import httpx
import asyncio


class TestBasicConnectivity:
    """Basic connectivity tests."""

    @pytest.mark.asyncio
    async def test_server_is_running(self):
        """Test that the server is running and accessible."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get("http://localhost:5002/")
                assert response.status_code == 200
                print(f"✅ Server is running and responded with status {response.status_code}")
        except httpx.ConnectError:
            pytest.fail("❌ Server is not running on port 5002. Please start the server first.")
        except Exception as e:
            pytest.fail(f"❌ Unexpected error connecting to server: {e}")

    @pytest.mark.asyncio
    async def test_health_endpoint_basic(self):
        """Test basic health endpoint accessibility."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get("http://localhost:5002/health")
                assert response.status_code == 200
                data = response.json()
                assert "status" in data
                print(f"✅ Health endpoint accessible, status: {data.get('status', 'unknown')}")
        except Exception as e:
            pytest.fail(f"❌ Health endpoint not accessible: {e}")

    @pytest.mark.asyncio
    async def test_list_codebases_basic(self):
        """Test basic codebase listing functionality."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post("http://localhost:5002/tools/list_codebases")
                assert response.status_code == 200
                data = response.json()
                assert "codebases" in data
                print(f"✅ Codebases endpoint accessible, found {len(data.get('codebases', []))} codebases")
        except Exception as e:
            pytest.fail(f"❌ Codebases endpoint not accessible: {e}")


if __name__ == "__main__":
    # Allow running this test file directly for quick checks
    asyncio.run(TestBasicConnectivity().test_server_is_running())
