"""
Test server health and basic connectivity.
"""
import pytest
import httpx


class TestServerHealth:
    """Test server health endpoints and basic connectivity."""

    @pytest.mark.asyncio
    async def test_root_endpoint(self, http_client: httpx.AsyncClient):
        """Test the root endpoint returns basic server information."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "server_name" in data
        assert "available_endpoints" in data
        assert isinstance(data["available_endpoints"], list)

    @pytest.mark.asyncio
    async def test_health_endpoint(self, http_client: httpx.AsyncClient, server_health_check):
        """Test the health endpoint returns comprehensive health information."""
        response = await http_client.get("/health")
        assert response.status_code == 200
        
        health_data = response.json()
        assert "status" in health_data
        assert "timestamp" in health_data
        assert "ollama_status" in health_data
        assert "chromadb_status" in health_data

    @pytest.mark.asyncio
    async def test_status_endpoint(self, http_client: httpx.AsyncClient):
        """Test the lightweight status endpoint."""
        response = await http_client.get("/status")
        assert response.status_code == 200
        
        status_data = response.json()
        assert "overall" in status_data
        assert "timestamp" in status_data
        assert status_data["overall"] in ["healthy", "running", "degraded"]

    @pytest.mark.asyncio
    async def test_detailed_health_endpoint(self, http_client: httpx.AsyncClient):
        """Test the detailed health endpoint."""
        response = await http_client.get("/health/detailed")
        assert response.status_code == 200
        
        detailed_health = response.json()
        assert "system_info" in detailed_health
        assert "service_status" in detailed_health
        assert "timestamp" in detailed_health

    @pytest.mark.asyncio
    async def test_enhanced_features_endpoint(self, http_client: httpx.AsyncClient):
        """Test the enhanced features documentation endpoint."""
        response = await http_client.get("/enhanced_features")
        assert response.status_code == 200
        
        features_data = response.json()
        assert "enhanced_features" in features_data
        assert isinstance(features_data["enhanced_features"], dict)
