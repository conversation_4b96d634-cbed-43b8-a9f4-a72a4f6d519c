@echo off
REM Unit test runner for Windows
REM This script runs the unit tests for the code analysis server

echo ========================================
echo OpenWebUI RAG Code Server Unit Tests
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if server is running
echo Checking if server is running on port 5002...
python -c "import requests; requests.get('http://localhost:5002/health', timeout=5)" >nul 2>&1
if errorlevel 1 (
    echo WARNING: Server may not be running on port 5002
    echo Please start the server before running tests
    echo.
    set /p choice="Continue anyway? (y/n): "
    if /i not "%choice%"=="y" exit /b 1
)

echo.
echo Running unit tests...
echo ========================================

REM Run the tests
python unit-tests/run_tests.py --verbose

echo.
echo ========================================
echo Test run completed
echo ========================================
pause
