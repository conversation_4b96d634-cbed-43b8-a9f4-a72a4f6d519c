#!/usr/bin/env python3
"""
Cleanup script to remove redundant test and debug files.
This script removes files that are no longer needed after consolidating
testing logic into the unit test framework.
"""

import os
import shutil
from pathlib import Path
from typing import List, Set


class TestingCleanup:
    """Handles cleanup of redundant testing files."""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.removed_files: List[str] = []
        self.removed_dirs: List[str] = []
        self.kept_files: List[str] = []
        
    def should_keep_file(self, file_path: Path) -> bool:
        """Determine if a file should be kept."""
        file_name = file_path.name.lower()
        
        # Keep core unit test framework files
        if file_path.parts and file_path.parts[0] == 'unit-tests':
            return True
            
        # Keep essential configuration files
        keep_patterns = {
            'pytest.ini',
            'requirements.txt',
            'docker-compose.yml',
            'dockerfile',
            'main.py',
            'run_unit_tests.bat',
            'run_unit_tests.sh',
            'unit_testing_setup.md',
            'testing_success_summary.md',
            'testing_consolidation_plan.md'
        }
        
        if file_name in keep_patterns:
            return True
            
        # Keep core application files
        core_app_patterns = {
            'framework_integration.py',
            'language_framework.py',
            'gpu_infrastructure.py',
            'processing_pipeline.py',
            'chunk_system.py',
            'vector_db_creator.py',
            'open_webui_code_analyzer_tool.py',
            'intent_detection_service.py'
        }
        
        if file_name in core_app_patterns:
            return True
            
        return False
    
    def get_files_to_remove(self) -> List[Path]:
        """Get list of files that should be removed."""
        files_to_remove = []
        
        # Debug testing directory - remove entirely
        debug_testing_dir = Path('debug-testing')
        if debug_testing_dir.exists():
            files_to_remove.append(debug_testing_dir)
        
        # Root level test files to remove
        root_test_patterns = [
            'test_*.py',
            'debug_*.py',
            'verify_*.py',
            'check_*.py',
            'fix_*.py',
            'quick_*.py',
            'simple_*.py',
            'run_all_tests.py',
            'run_core_tests.py',
            'performance_test_suite.py',
            'test_suite.py',
            'test_framework.py',
            'benchmark_*.py',
            'compare_*.py'
        ]
        
        for pattern in root_test_patterns:
            for file_path in Path('.').glob(pattern):
                if file_path.is_file() and not self.should_keep_file(file_path):
                    files_to_remove.append(file_path)
        
        # Legacy documentation files
        legacy_docs = [
            'README_TESTING.md',
            'TEST_SUITE_README.md',
            'INTENT_DETECTION_TESTING.md',
            'VSCODE_TESTING_TROUBLESHOOTING.md'
        ]
        
        for doc in legacy_docs:
            doc_path = Path(doc)
            if doc_path.exists():
                files_to_remove.append(doc_path)
        
        # Test result files
        for pattern in ['*.json', '*.md']:
            for file_path in Path('.').glob(pattern):
                if any(keyword in file_path.name.lower() for keyword in 
                      ['test_', 'debug_', 'ca_server_api_test', 'analysis_report']):
                    files_to_remove.append(file_path)
        
        return files_to_remove
    
    def remove_file_or_dir(self, path: Path) -> bool:
        """Remove a file or directory."""
        try:
            if self.dry_run:
                if path.is_dir():
                    print(f"[DRY RUN] Would remove directory: {path}")
                    # Count files in directory
                    file_count = sum(1 for _ in path.rglob('*') if _.is_file())
                    print(f"           Contains {file_count} files")
                else:
                    print(f"[DRY RUN] Would remove file: {path}")
                return True
            else:
                if path.is_dir():
                    shutil.rmtree(path)
                    self.removed_dirs.append(str(path))
                    print(f"✅ Removed directory: {path}")
                else:
                    path.unlink()
                    self.removed_files.append(str(path))
                    print(f"✅ Removed file: {path}")
                return True
        except Exception as e:
            print(f"❌ Error removing {path}: {e}")
            return False
    
    def run_cleanup(self):
        """Run the cleanup process."""
        print("🧹 Starting Testing Framework Cleanup")
        print("=" * 50)
        
        if self.dry_run:
            print("🔍 DRY RUN MODE - No files will actually be removed")
            print("   Run with --execute to perform actual cleanup")
            print()
        
        files_to_remove = self.get_files_to_remove()
        
        print(f"📊 Found {len(files_to_remove)} items to remove:")
        print()
        
        # Group by type
        directories = [f for f in files_to_remove if f.is_dir()]
        files = [f for f in files_to_remove if f.is_file()]
        
        if directories:
            print(f"📁 Directories to remove ({len(directories)}):")
            for directory in directories:
                file_count = sum(1 for _ in directory.rglob('*') if _.is_file()) if directory.exists() else 0
                print(f"   - {directory} ({file_count} files)")
            print()
        
        if files:
            print(f"📄 Files to remove ({len(files)}):")
            for file_path in files[:20]:  # Show first 20
                print(f"   - {file_path}")
            if len(files) > 20:
                print(f"   ... and {len(files) - 20} more files")
            print()
        
        # Perform removal
        success_count = 0
        for item in files_to_remove:
            if self.remove_file_or_dir(item):
                success_count += 1
        
        print()
        print("📈 Cleanup Summary:")
        print(f"   - Items processed: {len(files_to_remove)}")
        print(f"   - Successfully removed: {success_count}")
        print(f"   - Directories removed: {len(self.removed_dirs)}")
        print(f"   - Files removed: {len(self.removed_files)}")
        
        if not self.dry_run:
            print()
            print("✅ Cleanup completed successfully!")
            print("🧪 Unit test framework remains intact with enhanced capabilities")
        else:
            print()
            print("🔍 Dry run completed. Use --execute to perform actual cleanup.")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Cleanup redundant test files")
    parser.add_argument(
        "--execute", 
        action="store_true", 
        help="Actually perform the cleanup (default is dry run)"
    )
    
    args = parser.parse_args()
    
    cleanup = TestingCleanup(dry_run=not args.execute)
    cleanup.run_cleanup()


if __name__ == "__main__":
    main()
